<script setup lang="ts">
import type { IMetaText } from '@/api/interface/system/meta/mateText'
import { getMetaTextByKeyApi } from '@/api/modules/system/meta/mateText'
import { TextBusinessType } from '@/enums/meta/TextBusinessType'
import { TextType } from '@/enums/meta/TextType'

defineOptions({
  options: {
    styleIsolation: 'shared',
  },
})
const props = withDefaults(defineProps<Props>(), {
  showLoading: true,
  customClass: '',
  cache: true,
})

const emit = defineEmits<{
  loaded: [data: IMetaText.MetaText]
  error: [error: any]
  click: [data: IMetaText.MetaText]
}>()

interface Props {
  textKey: string
  showLoading?: boolean
  customClass?: string
  cache?: boolean
}

const loading = ref(false)
const textData = ref<IMetaText.MetaText | null>(null)
const showDetailModal = ref(false)
const cache = new Map<string, IMetaText.MetaText>()

async function fetchTextData() {
  if (!props.textKey)
    return

  if (props.cache && cache.has(props.textKey)) {
    textData.value = cache.get(props.textKey)!
    emit('loaded', textData.value)
    return
  }

  try {
    loading.value = true
    const response = await getMetaTextByKeyApi(props.textKey)

    if (response.data) {
      textData.value = response.data
      if (props.cache) {
        cache.set(props.textKey, response.data)
      }
      emit('loaded', response.data)
    }
  }
  catch (error) {
    console.error('TextShow: 获取文本数据失败', error)
    emit('error', error)
  }
  finally {
    loading.value = false
  }
}

function handleNavigation() {
  if (!textData.value?.skipUrl)
    return

  const url = textData.value.skipUrl

  if (url.startsWith('http://') || url.startsWith('https://')) {
    uni.showModal({
      title: '提示',
      content: '即将跳转到外部链接',
      success: (res) => {
        if (res.confirm) {
          uni.showToast({
            title: '请在浏览器中打开',
            icon: 'none',
          })
        }
      },
    })
  }
  else {
    uni.navigateTo({
      url,
      fail: () => {
        uni.switchTab({ url })
      },
    })
  }
}

function handleClick() {
  if (!textData.value)
    return

  emit('click', textData.value)

  const businessType = textData.value.businessType

  // 只有Cell和Agreement类型才显示弹窗
  if (businessType === TextBusinessType.AGREEMENT.code || businessType === TextBusinessType.Cell.code) {
    showDetailModal.value = true
    return
  }

  // 其他类型如果有跳转URL则跳转
  if (textData.value.skipUrl) {
    handleNavigation()
  }
}

function closeDetailModal() {
  showDetailModal.value = false
}

function getPreviewText(content: string): string {
  if (!content)
    return ''
  const plainText = content.replace(/<[^>]*>/g, '')
  return plainText.length > 50 ? `${plainText.substring(0, 50)}...` : plainText
}

function getPreviewContent(content: string): string {
  if (!content)
    return ''
  const plainText = content.replace(/<[^>]*>/g, '')
  if (plainText.length > 50) {
    return `${plainText.substring(0, 50)}...`
  }
  return content
}

watch(() => props.textKey, () => {
  if (props.textKey) {
    fetchTextData()
  }
}, { immediate: true })

onMounted(() => {
  if (props.textKey) {
    fetchTextData()
  }
})
</script>

<template>
  <view class="text-show-container" :class="[props.customClass]">
    <!-- 加载状态 -->
    <view v-if="loading && props.showLoading" class="loading-container">
      <wd-loading type="outline" size="32rpx" />
      <text class="loading-text">
        加载中...
      </text>
    </view>

    <!-- 内容展示 -->
    <view v-else-if="textData" class="content-container">
      <!-- 公告类型 -->
      <view
        v-if="textData.businessType === TextBusinessType.ANNOUNCEMENT.code"
        class="announcement-container"
        @tap="handleClick"
      >
        <view class="announcement-header">
          <wd-icon v-if="textData.icon" :name="textData.icon" size="32rpx" color="#FF9500" />
          <text class="announcement-title">
            {{ textData.title || textData.name }}
          </text>
        </view>
        <view v-if="textData.content" class="announcement-content">
          <rich-text
            v-if="textData.textType === TextType.RICH_TEXT.code"
            :nodes="textData.content"
            class="rich-text"
          />
          <text v-else class="normal-text">
            {{ textData.content }}
          </text>
        </view>
      </view>

      <!-- 信息类型 -->
      <view
        v-else-if="textData.businessType === TextBusinessType.INFORMATION.code"
        class="information-container"
        @tap="handleClick"
      >
        <view class="information-header">
          <wd-icon v-if="textData.icon" :name="textData.icon" size="32rpx" color="#34C759" />
          <text class="information-title">
            {{ textData.title || textData.name }}
          </text>
        </view>
        <view v-if="textData.content" class="information-content">
          <rich-text
            v-if="textData.textType === TextType.RICH_TEXT.code"
            :nodes="textData.content"
            class="rich-text"
          />
          <text v-else class="normal-text">
            {{ textData.content }}
          </text>
        </view>
      </view>

      <!-- 协议类型 -->
      <view
        v-else-if="textData.businessType === TextBusinessType.AGREEMENT.code"
        class="agreement-container"
        @tap="handleClick"
      >
        <view class="agreement-item">
          <view class="agreement-header">
            <wd-icon v-if="textData.icon" :name="textData.icon" size="32rpx" color="#007AFF" />
            <text class="agreement-title">
              {{ textData.title || textData.name }}
            </text>
            <text class="agreement-subtitle">点击查看详情</text>
          </view>
          <wd-icon name="arrow-right" size="28rpx" color="#C7C7CC" />
        </view>
      </view>

      <!-- 格子类型 -->
      <view
        v-else-if="textData.businessType === TextBusinessType.Cell.code"
        class="cell-container"
        @tap="handleClick"
      >
        <view class="cell-item">
          <view class="cell-icon">
            <wd-icon v-if="textData.icon" :name="textData.icon" size="40rpx" color="#007AFF" />
          </view>
          <view class="cell-content">
            <view class="cell-title">
              {{ textData.title || textData.name }}
            </view>
            <view v-if="textData.content" class="cell-subtitle">
              <rich-text
                v-if="textData.textType === TextType.RICH_TEXT.code"
                :nodes="getPreviewContent(textData.content)"
                class="rich-text-preview"
              />
              <text v-else class="normal-text-preview">
                {{ getPreviewText(textData.content) }}
              </text>
            </view>
          </view>
          <wd-icon name="arrow-right" size="28rpx" color="#C7C7CC" />
        </view>
      </view>

      <!-- 默认类型 -->
      <view v-else class="default-container" @tap="handleClick">
        <view v-if="textData.content">
          <rich-text
            v-if="textData.textType === TextType.RICH_TEXT.code"
            :nodes="textData.content"
            class="rich-text"
          />
          <text v-else class="normal-text">
            {{ textData.content }}
          </text>
        </view>
      </view>
    </view>

    <!-- 错误状态 -->
    <view v-else class="error-container">
      <wd-icon name="warning" size="32rpx" color="#FF3B30" />
      <text class="error-text">
        加载失败
      </text>
    </view>

    <!-- Apple风格详情弹窗 -->
    <wd-popup
      v-model="showDetailModal"
      position="center"
      :lock-scroll="true"
      :z-index="99999"
      custom-class="apple-modal-popup"
    >
      <view class="apple-modal">
        <view class="modal-backdrop" @tap="closeDetailModal" />
        <view class="modal-content">
          <view class="modal-header">
            <view class="header-title">
              {{ textData?.title || textData?.name }}
            </view>
            <view class="close-button" @tap="closeDetailModal">
              <view class="close-icon">
                <view class="close-line close-line-1" />
                <view class="close-line close-line-2" />
              </view>
            </view>
          </view>
          <scroll-view class="modal-body" scroll-y enhanced :show-scrollbar="false">
            <view v-if="textData?.content" class="content-wrapper">
              <rich-text
                v-if="textData.textType === TextType.RICH_TEXT.code"
                :nodes="textData.content"
                class="rich-content"
              />
              <text v-else class="plain-content">
                {{ textData.content }}
              </text>
            </view>
          </scroll-view>
          <view class="modal-footer">
            <view class="action-button" @tap="closeDetailModal">
              <text class="button-text">
                完成
              </text>
            </view>
          </view>
        </view>
      </view>
    </wd-popup>
  </view>
</template>

<style lang="scss" scoped>
$system-blue: #007aff;
$system-green: #34c759;
$system-orange: #ff9500;
$system-red: #ff3b30;
$system-gray6: #f2f2f7;
$label-primary: #000000;
$label-secondary: rgba(60, 60, 67, 0.6);
$background-primary: #ffffff;

.text-show-container {
  width: 100%;
}

.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  padding: 40rpx;

  .loading-text {
    font-size: 26rpx;
    color: $label-secondary;
  }
}

.error-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  padding: 40rpx;

  .error-text {
    font-size: 26rpx;
    color: $system-red;
  }
}

.announcement-container {
  padding: 24rpx;
  background: rgba(255, 149, 0, 0.05);
  border-radius: 16rpx;
  border: 1rpx solid rgba(255, 149, 0, 0.2);

  .announcement-header {
    display: flex;
    align-items: center;
    gap: 12rpx;
    margin-bottom: 16rpx;

    .announcement-title {
      font-size: 28rpx;
      font-weight: 600;
      color: $system-orange;
    }
  }

  .announcement-content {
    .rich-text,
    .normal-text {
      font-size: 26rpx;
      color: $label-secondary;
      line-height: 1.6;
    }
  }

  &:active {
    background: rgba(255, 149, 0, 0.1);
  }
}

.information-container {
  padding: 24rpx;
  background: rgba(52, 199, 89, 0.05);
  border-radius: 16rpx;
  border: 1rpx solid rgba(52, 199, 89, 0.2);

  .information-header {
    display: flex;
    align-items: center;
    gap: 12rpx;
    margin-bottom: 16rpx;

    .information-title {
      font-size: 28rpx;
      font-weight: 600;
      color: $system-green;
    }
  }

  .information-content {
    .rich-text,
    .normal-text {
      font-size: 26rpx;
      color: $label-secondary;
      line-height: 1.6;
    }
  }

  &:active {
    background: rgba(52, 199, 89, 0.1);
  }
}

.agreement-container {
  background: $background-primary;
  border-radius: 16rpx;
  border: 1rpx solid rgba(0, 0, 0, 0.06);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);

  .agreement-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 32rpx;
    transition: background-color 0.2s ease;

    .agreement-header {
      display: flex;
      align-items: center;
      gap: 16rpx;
      flex: 1;

      .agreement-title {
        font-size: 30rpx;
        font-weight: 500;
        color: $label-primary;
      }
    }

    &:active {
      background: rgba(0, 0, 0, 0.02);
    }
  }
}

.cell-container {
  background: $background-primary;
  border-radius: 16rpx;
  border: 1rpx solid rgba(0, 0, 0, 0.06);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);

  .cell-item {
    display: flex;
    align-items: center;
    padding: 32rpx;
    transition: background-color 0.2s ease;

    .cell-icon {
      width: 72rpx;
      height: 72rpx;
      background: rgba(0, 122, 255, 0.08);
      border-radius: 18rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 32rpx;
    }

    .cell-content {
      flex: 1;

      .cell-title {
        font-size: 30rpx;
        font-weight: 500;
        color: $label-primary;
        margin-bottom: 8rpx;
      }

      .cell-subtitle {
        font-size: 24rpx;
        color: $label-secondary;
        line-height: 1.4;

        .rich-text-preview,
        .normal-text-preview {
          font-size: 24rpx;
          color: $label-secondary;
          line-height: 1.4;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }

    &:active {
      background: rgba(0, 0, 0, 0.02);
    }
  }
}

.default-container {
  padding: 24rpx;

  .rich-text,
  .normal-text {
    font-size: 28rpx;
    color: $label-secondary;
    line-height: 1.6;
  }
}

:deep(.apple-modal-popup) {
  z-index: 99999 !important;

  .wd-popup__content {
    width: 100vw;
    height: 100vh;
    background: transparent;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40rpx;
    box-sizing: border-box;
  }
}

.apple-modal {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;

  .modal-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.4);
    backdrop-filter: blur(20rpx);
    -webkit-backdrop-filter: blur(20rpx);
  }

  .modal-content {
    position: relative;
    width: 100%;
    max-width: 640rpx;
    max-height: 80vh;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(40rpx);
    -webkit-backdrop-filter: blur(40rpx);
    border-radius: 28rpx;
    box-shadow:
      0 20rpx 60rpx rgba(0, 0, 0, 0.15),
      0 8rpx 24rpx rgba(0, 0, 0, 0.1),
      inset 0 1rpx 0 rgba(255, 255, 255, 0.8);
    border: 1rpx solid rgba(255, 255, 255, 0.2);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    animation: modalSlideIn 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  .modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 40rpx 40rpx 24rpx;
    border-bottom: 1rpx solid rgba(0, 0, 0, 0.06);

    .header-title {
      font-size: 36rpx;
      font-weight: 700;
      color: $label-primary;
      flex: 1;
      text-align: center;
      letter-spacing: -0.8rpx;
    }

    .close-button {
      position: absolute;
      top: 32rpx;
      right: 32rpx;
      width: 56rpx;
      height: 56rpx;
      background: rgba(0, 0, 0, 0.04);
      border-radius: 28rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;

      &:active {
        background: rgba(0, 0, 0, 0.08);
        transform: scale(0.95);
      }

      .close-icon {
        position: relative;
        width: 24rpx;
        height: 24rpx;

        .close-line {
          position: absolute;
          width: 24rpx;
          height: 2rpx;
          background: rgba(0, 0, 0, 0.6);
          border-radius: 1rpx;
          top: 50%;
          left: 50%;
          transform-origin: center;

          &.close-line-1 {
            transform: translate(-50%, -50%) rotate(45deg);
          }

          &.close-line-2 {
            transform: translate(-50%, -50%) rotate(-45deg);
          }
        }
      }
    }
  }

  .modal-body {
    flex: 1;
    padding: 0 40rpx;
    max-height: 60vh;
    overflow-y: auto;

    .content-wrapper {
      padding: 16rpx 0 32rpx;

      .rich-content,
      .plain-content {
        font-size: 30rpx;
        color: rgba(60, 60, 67, 0.8);
        line-height: 1.7;
        word-break: break-all;
      }

      .rich-content {
        :deep(p) {
          margin-bottom: 20rpx;
          line-height: 1.7;
        }

        :deep(h3) {
          font-size: 36rpx;
          font-weight: 700;
          color: $label-primary;
          margin: 32rpx 0 20rpx 0;
          letter-spacing: -0.5rpx;
        }

        :deep(h4) {
          font-size: 32rpx;
          font-weight: 600;
          color: $label-primary;
          margin: 28rpx 0 16rpx 0;
          letter-spacing: -0.3rpx;
        }

        :deep(ul),
        :deep(ol) {
          padding-left: 40rpx;
          margin: 20rpx 0;
        }

        :deep(li) {
          margin-bottom: 12rpx;
          line-height: 1.6;
        }

        :deep(strong) {
          font-weight: 700;
          color: $label-primary;
        }

        // br标签在小程序中不支持选择器，已移除

        :deep(img) {
          max-width: 100%;
          height: auto;
          border-radius: 12rpx;
          margin: 20rpx 0;
          box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
        }

        :deep(a) {
          color: $system-blue;
          text-decoration: none;
          font-weight: 500;
        }
      }

      .plain-content {
        white-space: pre-wrap;
      }
    }
  }

  .modal-footer {
    padding: 24rpx 40rpx 40rpx;
    border-top: 1rpx solid rgba(0, 0, 0, 0.06);

    .action-button {
      width: 100%;
      height: 88rpx;
      background: $system-blue;
      border-radius: 22rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;
      box-shadow: 0 4rpx 16rpx rgba(0, 122, 255, 0.3);

      &:active {
        background: #0056b3;
        transform: scale(0.98);
        box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.4);
      }

      .button-text {
        font-size: 32rpx;
        font-weight: 600;
        color: #ffffff;
        letter-spacing: -0.5rpx;
      }
    }
  }
}

@keyframes modalSlideIn {
  0% {
    opacity: 0;
    transform: scale(0.9) translateY(40rpx);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}
</style>
